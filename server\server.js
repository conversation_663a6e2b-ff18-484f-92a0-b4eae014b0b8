const express = require("express");
const cors = require("cors");
require("dotenv").config();

console.log("启动服务器...");

// 初始化Express应用
const app = express();

// 中间件
app.use(cors());
app.use(express.json());

console.log("设置路由...");

// 路由
try {
  app.use("/api/users", require("./routes/users"));
  app.use("/api/novels", require("./routes/novels"));
  console.log("路由设置成功");
} catch (error) {
  console.error("路由设置失败:", error);
  process.exit(1);
}

// 简单的测试路由
app.get("/", (req, res) => {
  res.send("小说创作网API正在运行");
});

// 错误处理中间件
app.use((err, req, res, next) => {
  console.error("服务器错误:", err.stack);
  res.status(500).json({ msg: "服务器内部错误" });
});

// 设置端口并启动服务器
const PORT = process.env.PORT || 5000;
const server = app.listen(PORT, () => {
  console.log(`服务器运行在端口 ${PORT}`);
  console.log(`API地址: http://localhost:${PORT}/api`);
});

// 优雅关闭
process.on("SIGTERM", () => {
  console.log("收到SIGTERM信号，正在关闭服务器...");
  server.close(() => {
    console.log("服务器已关闭");
    process.exit(0);
  });
});

process.on("SIGINT", () => {
  console.log("收到SIGINT信号，正在关闭服务器...");
  server.close(() => {
    console.log("服务器已关闭");
    process.exit(0);
  });
});

// 捕获未处理的异常
process.on("uncaughtException", (err) => {
  console.error("未捕获的异常:", err);
  process.exit(1);
});

process.on("unhandledRejection", (reason, promise) => {
  console.error("未处理的Promise拒绝:", reason);
  process.exit(1);
});
