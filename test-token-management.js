// 令牌管理测试脚本
// 在浏览器控制台中运行此脚本来测试令牌管理功能

console.log('=== 令牌管理测试 ===');

// 测试1: 检查令牌管理器是否正确导入
console.log('1. 检查令牌管理器...');
if (typeof tokenManager !== 'undefined') {
  console.log('✓ 令牌管理器已加载');
} else {
  console.log('✗ 令牌管理器未找到');
}

// 测试2: 检查当前登录状态
console.log('2. 检查登录状态...');
const isLoggedIn = localStorage.getItem('token') !== null;
console.log(`当前登录状态: ${isLoggedIn ? '已登录' : '未登录'}`);

if (isLoggedIn) {
  const token = localStorage.getItem('token');
  const user = localStorage.getItem('user');
  
  console.log('令牌存在:', !!token);
  console.log('用户信息存在:', !!user);
  
  if (token) {
    try {
      // 解析令牌
      const payload = JSON.parse(atob(token.split('.')[1]));
      const currentTime = Date.now() / 1000;
      const timeUntilExpiry = payload.exp - currentTime;
      
      console.log('令牌信息:');
      console.log('- 用户名:', payload.username);
      console.log('- 过期时间:', new Date(payload.exp * 1000).toLocaleString());
      console.log('- 剩余时间:', Math.floor(timeUntilExpiry / 60), '分钟');
      console.log('- 是否即将过期:', timeUntilExpiry < 3600 ? '是' : '否');
      
    } catch (error) {
      console.log('✗ 令牌解析失败:', error.message);
    }
  }
}

// 测试3: 测试API调用
console.log('3. 测试API调用...');
if (isLoggedIn) {
  fetch('http://localhost:5000/api/users/me', {
    headers: {
      'x-auth-token': localStorage.getItem('token'),
      'Content-Type': 'application/json'
    }
  })
  .then(response => {
    console.log('API响应状态:', response.status);
    if (response.status === 200) {
      console.log('✓ API调用成功');
      return response.json();
    } else if (response.status === 401) {
      console.log('✗ 令牌无效或已过期');
      return response.json();
    } else {
      console.log('✗ API调用失败');
    }
  })
  .then(data => {
    if (data) {
      console.log('响应数据:', data);
    }
  })
  .catch(error => {
    console.log('✗ 网络错误:', error.message);
  });
} else {
  console.log('跳过API测试 - 用户未登录');
}

// 测试4: 测试令牌刷新API
console.log('4. 测试令牌刷新...');
if (isLoggedIn) {
  fetch('http://localhost:5000/api/users/refresh-token', {
    method: 'POST',
    headers: {
      'x-auth-token': localStorage.getItem('token'),
      'Content-Type': 'application/json'
    }
  })
  .then(response => {
    console.log('刷新API响应状态:', response.status);
    if (response.status === 200) {
      console.log('✓ 令牌刷新成功');
      return response.json();
    } else {
      console.log('✗ 令牌刷新失败');
      return response.json();
    }
  })
  .then(data => {
    if (data && data.token) {
      console.log('新令牌已获取');
      console.log('用户信息:', data.user);
      
      // 可选：更新本地存储
      // localStorage.setItem('token', data.token);
      // localStorage.setItem('user', JSON.stringify(data.user));
      // console.log('✓ 本地存储已更新');
    }
  })
  .catch(error => {
    console.log('✗ 刷新请求失败:', error.message);
  });
} else {
  console.log('跳过刷新测试 - 用户未登录');
}

console.log('=== 测试完成 ===');
console.log('请检查上述输出以验证令牌管理功能是否正常工作');
console.log('如果发现问题，请检查:');
console.log('1. 后端服务器是否运行在端口5000');
console.log('2. 前端应用是否正确加载');
console.log('3. 用户是否已登录');
console.log('4. 网络连接是否正常');
