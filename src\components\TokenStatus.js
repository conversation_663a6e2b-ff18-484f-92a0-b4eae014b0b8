import React, { useState, useEffect } from 'react';
import tokenManager from '../utils/tokenManager';

const TokenStatus = () => {
  const [tokenInfo, setTokenInfo] = useState(null);
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const updateTokenInfo = () => {
      const token = localStorage.getItem('token');
      if (token && tokenManager.isLoggedIn()) {
        try {
          const payload = JSON.parse(atob(token.split('.')[1]));
          const currentTime = Date.now() / 1000;
          const timeUntilExpiry = payload.exp - currentTime;
          
          setTokenInfo({
            expiresAt: new Date(payload.exp * 1000),
            timeUntilExpiry: timeUntilExpiry,
            isExpiringSoon: timeUntilExpiry < 3600, // 1小时内过期
            username: payload.username
          });
          
          // 只在令牌即将过期时显示
          setIsVisible(timeUntilExpiry < 3600 && timeUntilExpiry > 0);
        } catch (error) {
          console.error('解析令牌失败:', error);
          setTokenInfo(null);
          setIsVisible(false);
        }
      } else {
        setTokenInfo(null);
        setIsVisible(false);
      }
    };

    // 初始更新
    updateTokenInfo();

    // 每分钟更新一次
    const interval = setInterval(updateTokenInfo, 60000);

    return () => clearInterval(interval);
  }, []);

  const formatTimeRemaining = (seconds) => {
    if (seconds <= 0) return '已过期';
    
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    
    if (hours > 0) {
      return `${hours}小时${minutes}分钟`;
    } else {
      return `${minutes}分钟`;
    }
  };

  const handleRefreshToken = async () => {
    try {
      const success = await tokenManager.refreshToken();
      if (success) {
        // 刷新成功后重新计算令牌信息
        const token = localStorage.getItem('token');
        if (token) {
          const payload = JSON.parse(atob(token.split('.')[1]));
          const currentTime = Date.now() / 1000;
          const timeUntilExpiry = payload.exp - currentTime;
          
          setTokenInfo({
            expiresAt: new Date(payload.exp * 1000),
            timeUntilExpiry: timeUntilExpiry,
            isExpiringSoon: timeUntilExpiry < 3600,
            username: payload.username
          });
          
          setIsVisible(timeUntilExpiry < 3600 && timeUntilExpiry > 0);
        }
      }
    } catch (error) {
      console.error('刷新令牌失败:', error);
    }
  };

  if (!isVisible || !tokenInfo) {
    return null;
  }

  return (
    <div className="alert alert-warning alert-dismissible fade show position-fixed" 
         style={{ 
           top: '70px', 
           right: '20px', 
           zIndex: 1050, 
           maxWidth: '400px',
           minWidth: '300px'
         }}>
      <div className="d-flex align-items-center">
        <i className="bi bi-exclamation-triangle-fill me-2"></i>
        <div className="flex-grow-1">
          <strong>登录即将过期</strong>
          <br />
          <small>
            剩余时间: {formatTimeRemaining(tokenInfo.timeUntilExpiry)}
            <br />
            过期时间: {tokenInfo.expiresAt.toLocaleString()}
          </small>
        </div>
      </div>
      <div className="mt-2">
        <button 
          className="btn btn-sm btn-warning me-2" 
          onClick={handleRefreshToken}
        >
          延长登录
        </button>
        <button 
          type="button" 
          className="btn-close" 
          onClick={() => setIsVisible(false)}
          aria-label="Close"
        ></button>
      </div>
    </div>
  );
};

export default TokenStatus;
