const express = require("express");
const cors = require("cors");
const jwt = require("jsonwebtoken");
const bcrypt = require("bcryptjs");
const mysql = require("mysql2/promise");
require("dotenv").config();

const app = express();

// 中间件
app.use(cors());
app.use(express.json());

// 数据库连接池
const pool = mysql.createPool({
  host: process.env.DB_HOST,
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  database: process.env.DB_NAME,
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0,
});

// 测试路由
app.get("/", (req, res) => {
  res.json({ message: "小说创作网API正在运行", timestamp: new Date().toISOString() });
});

app.get("/api/test", (req, res) => {
  res.json({ message: "API测试成功", timestamp: new Date().toISOString() });
});

// 登录路由
app.post("/api/users/login", async (req, res) => {
  console.log('收到登录请求:', req.body);
  const { username, password } = req.body;

  if (!username || !password) {
    return res.status(400).json({ msg: "请提供用户名和密码" });
  }

  try {
    // 查找用户
    const [rows] = await pool.query("SELECT * FROM users WHERE username = ?", [username]);
    const user = rows[0];
    
    if (!user) {
      return res.status(400).json({ msg: "用户不存在" });
    }

    // 验证密码
    const isMatch = await bcrypt.compare(password, user.password);
    if (!isMatch) {
      return res.status(400).json({ msg: "密码错误" });
    }

    // 生成JWT令牌
    const token = jwt.sign(
      {
        id: user.id,
        username: user.username,
        email: user.email,
        is_admin: user.is_admin,
      },
      process.env.JWT_SECRET,
      { expiresIn: "24h" }
    );

    console.log('登录成功:', user.username);
    res.json({
      token,
      user: {
        id: user.id,
        username: user.username,
        email: user.email,
        is_admin: user.is_admin,
      },
    });
  } catch (error) {
    console.error('登录错误:', error);
    res.status(500).json({ msg: "服务器错误" });
  }
});

// 注册路由
app.post("/api/users/register", async (req, res) => {
  console.log('收到注册请求:', req.body);
  const { username, email, password } = req.body;

  if (!username || !email || !password) {
    return res.status(400).json({ msg: "请提供所有必需字段" });
  }

  try {
    // 检查用户名是否已存在
    const [existingUsers] = await pool.query("SELECT * FROM users WHERE username = ?", [username]);
    if (existingUsers.length > 0) {
      return res.status(400).json({ msg: "用户名已存在" });
    }

    // 检查邮箱是否已存在
    const [existingEmails] = await pool.query("SELECT * FROM users WHERE email = ?", [email]);
    if (existingEmails.length > 0) {
      return res.status(400).json({ msg: "邮箱已被注册" });
    }

    // 加密密码
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(password, salt);

    // 创建新用户
    const [result] = await pool.query(
      "INSERT INTO users (username, email, password) VALUES (?, ?, ?)",
      [username, email, hashedPassword]
    );

    // 生成JWT令牌
    const token = jwt.sign(
      { id: result.insertId, username, email },
      process.env.JWT_SECRET,
      { expiresIn: "24h" }
    );

    console.log('注册成功:', username);
    res.status(201).json({
      token,
      user: {
        id: result.insertId,
        username,
        email,
      },
    });
  } catch (error) {
    console.error('注册错误:', error);
    res.status(500).json({ msg: "服务器错误" });
  }
});

// 错误处理
app.use((err, req, res, next) => {
  console.error('服务器错误:', err);
  res.status(500).json({ msg: "服务器内部错误" });
});

const PORT = process.env.PORT || 5000;

console.log('启动最小化服务器...');
const server = app.listen(PORT, () => {
  console.log(`✓ 服务器运行在端口 ${PORT}`);
  console.log(`✓ API地址: http://localhost:${PORT}/api`);
  console.log('✓ 服务器启动完成');
});

// 优雅关闭
process.on('SIGINT', () => {
  console.log('\n正在关闭服务器...');
  server.close(async () => {
    console.log('关闭数据库连接池...');
    await pool.end();
    console.log('服务器已关闭');
    process.exit(0);
  });
});

// 错误处理
process.on('uncaughtException', (err) => {
  console.error('未捕获的异常:', err);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('未处理的Promise拒绝:', reason);
});

console.log('等待连接...');
