const axios = require("axios");

async function testLogin() {
  console.log("测试登录API...");

  try {
    // 测试基本连接
    console.log("1. 测试服务器连接...");
    const healthResponse = await axios.get("http://localhost:5000/");
    console.log("✓ 服务器连接成功:", healthResponse.data);

    // 测试登录 - 使用数据库中存在的用户
    console.log("2. 测试登录...");
    const loginData = {
      username: "123456",
      password: "123456", // 假设密码和用户名相同
    };

    const loginResponse = await axios.post(
      "http://localhost:5000/api/users/login",
      loginData
    );
    console.log("✓ 登录成功!");
    console.log("令牌:", loginResponse.data.token);
    console.log("用户信息:", loginResponse.data.user);
  } catch (error) {
    console.error("✗ 测试失败:");
    if (error.response) {
      console.error("状态码:", error.response.status);
      console.error("错误信息:", error.response.data);
    } else if (error.request) {
      console.error("网络错误:", error.message);
    } else {
      console.error("其他错误:", error.message);
    }
  }
}

testLogin();
