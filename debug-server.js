console.log('1. 开始启动服务器...');

try {
  console.log('2. 导入 express...');
  const express = require("express");
  console.log('✓ express 导入成功');

  console.log('3. 导入 cors...');
  const cors = require("cors");
  console.log('✓ cors 导入成功');

  console.log('4. 加载环境变量...');
  require("dotenv").config();
  console.log('✓ 环境变量加载成功');

  console.log('5. 创建 Express 应用...');
  const app = express();
  console.log('✓ Express 应用创建成功');

  console.log('6. 设置中间件...');
  app.use(cors());
  app.use(express.json());
  console.log('✓ 中间件设置成功');

  console.log('7. 导入用户路由...');
  const userRoutes = require("./server/routes/users");
  console.log('✓ 用户路由导入成功');

  console.log('8. 导入小说路由...');
  const novelRoutes = require("./server/routes/novels");
  console.log('✓ 小说路由导入成功');

  console.log('9. 设置路由...');
  app.use("/api/users", userRoutes);
  app.use("/api/novels", novelRoutes);
  console.log('✓ 路由设置成功');

  console.log('10. 设置根路由...');
  app.get("/", (req, res) => {
    res.send("小说创作网API正在运行");
  });
  console.log('✓ 根路由设置成功');

  console.log('11. 启动服务器...');
  const PORT = process.env.PORT || 5000;
  const server = app.listen(PORT, () => {
    console.log(`✓ 服务器成功运行在端口 ${PORT}`);
    console.log(`API地址: http://localhost:${PORT}/api`);
    console.log('服务器启动完成！');
  });

  // 保持服务器运行
  process.on('SIGINT', () => {
    console.log('\n收到中断信号，关闭服务器...');
    server.close(() => {
      console.log('服务器已关闭');
      process.exit(0);
    });
  });

  console.log('12. 事件监听器设置完成');

} catch (error) {
  console.error('✗ 服务器启动失败:', error.message);
  console.error('错误堆栈:', error.stack);
  process.exit(1);
}
