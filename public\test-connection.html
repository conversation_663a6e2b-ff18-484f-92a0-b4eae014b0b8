<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>连接测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .result { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        button { padding: 10px 20px; margin: 5px; cursor: pointer; }
    </style>
</head>
<body>
    <h1>前后端连接测试</h1>
    
    <button onclick="testServerConnection()">测试服务器连接</button>
    <button onclick="testLogin()">测试登录</button>
    <button onclick="clearResults()">清除结果</button>
    
    <div id="results"></div>

    <script>
        function addResult(message, isSuccess = true) {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `result ${isSuccess ? 'success' : 'error'}`;
            div.innerHTML = `${new Date().toLocaleTimeString()}: ${message}`;
            results.appendChild(div);
        }

        async function testServerConnection() {
            addResult('开始测试服务器连接...');
            
            try {
                const response = await fetch('http://localhost:5000/');
                const data = await response.json();
                addResult(`✓ 服务器连接成功: ${data.message}`, true);
            } catch (error) {
                addResult(`✗ 服务器连接失败: ${error.message}`, false);
            }
        }

        async function testLogin() {
            addResult('开始测试登录...');
            
            try {
                const loginData = {
                    username: '1234567',
                    password: '1234567'
                };
                
                addResult(`发送登录请求: 用户名=${loginData.username}`);
                
                const response = await fetch('http://localhost:5000/api/users/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(loginData)
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    addResult(`✓ 登录成功: 用户=${data.user.username}, ID=${data.user.id}`, true);
                } else {
                    addResult(`✗ 登录失败: ${data.msg}`, false);
                }
            } catch (error) {
                addResult(`✗ 登录请求失败: ${error.message}`, false);
            }
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }

        // 页面加载时自动测试连接
        window.onload = function() {
            addResult('页面加载完成，开始自动测试...');
            testServerConnection();
        };
    </script>
</body>
</html>
