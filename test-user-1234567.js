const axios = require('axios');

async function testUser1234567() {
  console.log('测试用户 1234567 登录...');
  
  try {
    // 测试服务器连接
    console.log('1. 测试服务器连接...');
    const healthResponse = await axios.get('http://localhost:5000/');
    console.log('✓ 服务器连接成功');
    
    // 尝试不同的密码组合
    const passwordsToTry = [
      '1234567',  // 和用户名相同
      '123456',   // 常见密码
      'password', // 默认密码
      '**********', // 扩展版本
      'admin',    // 管理员密码
      '123',      // 简单密码
    ];
    
    for (const password of passwordsToTry) {
      console.log(`2. 尝试密码: ${password}`);
      
      try {
        const loginData = {
          username: '1234567',
          password: password
        };
        
        const loginResponse = await axios.post('http://localhost:5000/api/users/login', loginData);
        console.log('✓ 登录成功!');
        console.log('用户信息:', loginResponse.data.user);
        console.log('令牌:', loginResponse.data.token.substring(0, 20) + '...');
        return; // 成功后退出
        
      } catch (error) {
        if (error.response && error.response.status === 400) {
          console.log(`✗ 密码 "${password}" 错误`);
        } else {
          console.error('✗ 其他错误:', error.response?.data || error.message);
        }
      }
    }
    
    console.log('❌ 所有密码都尝试失败了');
    console.log('建议：');
    console.log('1. 使用注册功能创建新账户');
    console.log('2. 或者联系管理员重置密码');
    
  } catch (error) {
    console.error('✗ 测试失败:', error.message);
  }
}

testUser1234567();
