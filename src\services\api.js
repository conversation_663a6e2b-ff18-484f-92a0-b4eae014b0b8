import axios from "axios";
import tokenManager from "../utils/tokenManager";

const API_URL = "http://localhost:5000/api";

// 创建axios实例
const api = axios.create({
  baseURL: API_URL,
  headers: {
    "Content-Type": "application/json",
  },
});

// 请求拦截器，添加token到请求头并自动刷新令牌
api.interceptors.request.use(
  async (config) => {
    // 跳过刷新令牌的请求，避免无限循环
    if (config.url === "/users/refresh-token") {
      const token = localStorage.getItem("token");
      if (token) {
        config.headers["x-auth-token"] = token;
      }
      return config;
    }

    // 检查并刷新令牌
    const isValid = await tokenManager.refreshTokenIfNeeded();
    if (!isValid) {
      // 令牌无效，重定向到登录页面
      window.location.href = "/login";
      return Promise.reject(new Error("令牌无效"));
    }

    const token = localStorage.getItem("token");
    if (token) {
      config.headers["x-auth-token"] = token;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器，处理401错误（令牌失效）
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    if (error.response && error.response.status === 401) {
      // 令牌失效，清除本地存储
      tokenManager.clearTokens();

      // 显示提示信息
      alert("登录已过期，请重新登录");

      // 重定向到登录页面
      window.location.href = "/login";
    }
    return Promise.reject(error);
  }
);

// 用户相关API
export const userAPI = {
  register: (userData) => api.post("/users/register", userData),
  login: (credentials) => api.post("/users/login", credentials),
  getCurrentUser: () => api.get("/users/me"),
  refreshToken: () => api.post("/users/refresh-token"),
};

// 小说相关API
export const novelAPI = {
  getAllNovels: () => api.get("/novels"),
  getNovelById: (id) => api.get(`/novels/${id}`),
  getUserNovels: () => api.get("/novels/user/me"),
  createNovel: (novelData) => api.post("/novels", novelData),
  updateNovel: (id, novelData) => api.put(`/novels/${id}`, novelData),
  deleteNovel: (id) => api.delete(`/novels/${id}`),
  getNovelComments: (novelId) => api.get(`/novels/${novelId}/comments`),
  addComment: (novelId, commentData) =>
    api.post(`/novels/${novelId}/comments`, commentData),
  deleteComment: (novelId, commentId) =>
    api.delete(`/novels/${novelId}/comments/${commentId}`),
};

// 评价相关API
export const ratingAPI = {
  getNovelRatingStats: (novelId) => api.get(`/novels/${novelId}/ratings/stats`),
  getUserRating: (novelId) => api.get(`/novels/${novelId}/ratings/user`),
  upsertRating: (novelId, ratingData) =>
    api.post(`/novels/${novelId}/ratings`, ratingData),
  deleteRating: (novelId) => api.delete(`/novels/${novelId}/ratings`),
  getNovelRatingsWithUsers: (novelId) => api.get(`/novels/${novelId}/ratings`),
};

export default api;
