const jwt = require("jsonwebtoken");
require("dotenv").config();

function auth(req, res, next) {
  // 从请求头获取token
  const token = req.header("x-auth-token");

  // 检查是否有token
  if (!token) {
    return res.status(401).json({ msg: "没有提供令牌，授权失败" });
  }

  try {
    // 验证token
    const decoded = jwt.verify(token, process.env.JWT_SECRET);

    // 将用户信息添加到请求对象
    req.user = decoded;
    next();
  } catch (err) {
    let errorMessage = "令牌无效";

    if (err.name === "TokenExpiredError") {
      errorMessage = "令牌已过期";
    } else if (err.name === "JsonWebTokenError") {
      errorMessage = "令牌格式错误";
    } else if (err.name === "NotBeforeError") {
      errorMessage = "令牌尚未生效";
    }

    console.log(`认证失败: ${errorMessage}`, err.message);
    res.status(401).json({
      msg: errorMessage,
      error: err.name,
      timestamp: new Date().toISOString(),
    });
  }
}

module.exports = auth;
