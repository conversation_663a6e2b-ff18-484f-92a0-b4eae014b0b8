const mysql = require("mysql2/promise");
require("dotenv").config();

async function checkUsers() {
  console.log('检查数据库中的用户...');
  
  try {
    const connection = await mysql.createConnection({
      host: process.env.DB_HOST,
      user: process.env.DB_USER,
      password: process.env.DB_PASSWORD,
      database: process.env.DB_NAME,
    });

    console.log('✓ 数据库连接成功');
    
    // 查询所有用户
    const [users] = await connection.query('SELECT id, username, email, is_admin, created_at FROM users');
    console.log('数据库中的用户:');
    console.table(users);
    
    await connection.end();
    console.log('✓ 连接已关闭');
    
  } catch (error) {
    console.error('✗ 查询失败:', error.message);
  }
}

checkUsers();
