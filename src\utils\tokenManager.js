// 令牌管理工具
import { userAPI } from '../services/api';

class TokenManager {
  constructor() {
    this.refreshPromise = null;
    this.isRefreshing = false;
  }

  // 检查令牌是否即将过期（在过期前1小时刷新）
  isTokenExpiringSoon(token) {
    if (!token) return true;
    
    try {
      const payload = JSON.parse(atob(token.split('.')[1]));
      const currentTime = Date.now() / 1000;
      const timeUntilExpiry = payload.exp - currentTime;
      
      // 如果令牌在1小时内过期，返回true
      return timeUntilExpiry < 3600; // 3600秒 = 1小时
    } catch (error) {
      console.error('解析令牌失败:', error);
      return true;
    }
  }

  // 检查令牌是否已过期
  isTokenExpired(token) {
    if (!token) return true;
    
    try {
      const payload = JSON.parse(atob(token.split('.')[1]));
      const currentTime = Date.now() / 1000;
      return payload.exp < currentTime;
    } catch (error) {
      console.error('解析令牌失败:', error);
      return true;
    }
  }

  // 自动刷新令牌
  async refreshTokenIfNeeded() {
    const token = localStorage.getItem('token');
    
    if (!token || this.isTokenExpired(token)) {
      this.clearTokens();
      return false;
    }

    if (this.isTokenExpiringSoon(token) && !this.isRefreshing) {
      return await this.refreshToken();
    }

    return true;
  }

  // 刷新令牌
  async refreshToken() {
    if (this.isRefreshing) {
      return this.refreshPromise;
    }

    this.isRefreshing = true;
    this.refreshPromise = this._performRefresh();

    try {
      const result = await this.refreshPromise;
      return result;
    } finally {
      this.isRefreshing = false;
      this.refreshPromise = null;
    }
  }

  async _performRefresh() {
    try {
      const response = await userAPI.refreshToken();
      
      // 更新本地存储
      localStorage.setItem('token', response.data.token);
      localStorage.setItem('user', JSON.stringify(response.data.user));
      
      console.log('令牌刷新成功');
      return true;
    } catch (error) {
      console.error('令牌刷新失败:', error);
      this.clearTokens();
      return false;
    }
  }

  // 清除令牌
  clearTokens() {
    localStorage.removeItem('token');
    localStorage.removeItem('user');
  }

  // 获取用户信息
  getUser() {
    const userData = localStorage.getItem('user');
    return userData ? JSON.parse(userData) : null;
  }

  // 检查是否已登录
  isLoggedIn() {
    const token = localStorage.getItem('token');
    return token && !this.isTokenExpired(token);
  }
}

// 创建单例实例
const tokenManager = new TokenManager();

export default tokenManager;
