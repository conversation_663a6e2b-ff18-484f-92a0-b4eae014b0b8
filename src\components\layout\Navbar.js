import React, { useState, useEffect } from "react";
import { Link, useNavigate } from "react-router-dom";
import tokenManager from "../../utils/tokenManager";

const Navbar = () => {
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [user, setUser] = useState(null);
  const navigate = useNavigate();

  useEffect(() => {
    // 使用令牌管理器检查登录状态
    if (tokenManager.isLoggedIn()) {
      setIsLoggedIn(true);
      setUser(tokenManager.getUser());
    }
  }, []);

  const handleLogout = () => {
    // 使用令牌管理器清除令牌
    tokenManager.clearTokens();

    // 更新状态
    setIsLoggedIn(false);
    setUser(null);

    // 重定向到首页
    navigate("/");
  };

  return (
    <nav className="navbar navbar-expand-lg navbar-dark bg-dark">
      <div className="container">
        <Link className="navbar-brand" to="/">
          小说创作网
        </Link>
        <button
          className="navbar-toggler"
          type="button"
          data-bs-toggle="collapse"
          data-bs-target="#navbarNav"
        >
          <span className="navbar-toggler-icon"></span>
        </button>
        <div className="collapse navbar-collapse" id="navbarNav">
          <ul className="navbar-nav me-auto">
            <li className="nav-item">
              <Link className="nav-link" to="/">
                首页
              </Link>
            </li>
            <li className="nav-item">
              <Link className="nav-link" to="/novels">
                作品展示
              </Link>
            </li>
            {isLoggedIn && (
              <li className="nav-item">
                <Link className="nav-link" to="/editor">
                  创作中心
                </Link>
              </li>
            )}
          </ul>
          <ul className="navbar-nav">
            {!isLoggedIn ? (
              <>
                <li className="nav-item">
                  <Link className="nav-link" to="/login">
                    登录
                  </Link>
                </li>
                <li className="nav-item">
                  <Link className="nav-link" to="/register">
                    注册
                  </Link>
                </li>
              </>
            ) : (
              <>
                <li className="nav-item">
                  <span className="nav-link">
                    欢迎，{user?.username} {user?.is_admin ? "(管理员)" : ""}
                  </span>
                </li>
                <li className="nav-item">
                  <button
                    className="nav-link btn btn-link"
                    onClick={handleLogout}
                  >
                    退出
                  </button>
                </li>
              </>
            )}
          </ul>
        </div>
      </div>
    </nav>
  );
};

export default Navbar;
