const mysql = require("mysql2/promise");
require("dotenv").config();

async function testConnection() {
  console.log('测试数据库连接...');
  console.log('数据库配置:');
  console.log('- 主机:', process.env.DB_HOST);
  console.log('- 用户:', process.env.DB_USER);
  console.log('- 数据库:', process.env.DB_NAME);
  
  try {
    const connection = await mysql.createConnection({
      host: process.env.DB_HOST,
      user: process.env.DB_USER,
      password: process.env.DB_PASSWORD,
      database: process.env.DB_NAME,
    });

    console.log('✓ 数据库连接成功');
    
    // 测试查询
    const [rows] = await connection.query('SELECT 1 as test');
    console.log('✓ 测试查询成功:', rows);
    
    // 检查用户表
    const [users] = await connection.query('SELECT COUNT(*) as count FROM users');
    console.log('✓ 用户表查询成功，用户数量:', users[0].count);
    
    await connection.end();
    console.log('✓ 连接已关闭');
    
  } catch (error) {
    console.error('✗ 数据库连接失败:', error.message);
    console.error('错误详情:', error);
  }
}

testConnection();
