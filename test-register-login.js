const axios = require('axios');

async function testRegisterAndLogin() {
  console.log('测试注册和登录功能...');
  
  try {
    // 测试基本连接
    console.log('1. 测试服务器连接...');
    const healthResponse = await axios.get('http://localhost:5000/');
    console.log('✓ 服务器连接成功:', healthResponse.data);
    
    // 测试注册新用户
    console.log('2. 测试注册新用户...');
    const registerData = {
      username: 'testuser' + Date.now(), // 使用时间戳确保用户名唯一
      email: 'test' + Date.now() + '@example.com',
      password: 'testpass123'
    };
    
    console.log('注册数据:', registerData);
    
    try {
      const registerResponse = await axios.post('http://localhost:5000/api/users/register', registerData);
      console.log('✓ 注册成功!');
      console.log('令牌:', registerResponse.data.token.substring(0, 20) + '...');
      console.log('用户信息:', registerResponse.data.user);
      
      // 测试用新注册的用户登录
      console.log('3. 测试用新用户登录...');
      const loginData = {
        username: registerData.username,
        password: registerData.password
      };
      
      const loginResponse = await axios.post('http://localhost:5000/api/users/login', loginData);
      console.log('✓ 登录成功!');
      console.log('令牌:', loginResponse.data.token.substring(0, 20) + '...');
      console.log('用户信息:', loginResponse.data.user);
      
      console.log('🎉 所有测试通过！登录功能正常工作！');
      
    } catch (registerError) {
      console.error('✗ 注册失败:');
      if (registerError.response) {
        console.error('状态码:', registerError.response.status);
        console.error('错误信息:', registerError.response.data);
      } else {
        console.error('错误:', registerError.message);
      }
    }
    
  } catch (error) {
    console.error('✗ 测试失败:');
    if (error.response) {
      console.error('状态码:', error.response.status);
      console.error('错误信息:', error.response.data);
    } else if (error.request) {
      console.error('网络错误:', error.message);
    } else {
      console.error('其他错误:', error.message);
    }
  }
}

testRegisterAndLogin();
