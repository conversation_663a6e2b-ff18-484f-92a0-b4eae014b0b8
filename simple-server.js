const express = require("express");
const cors = require("cors");

const app = express();

// 中间件
app.use(cors());
app.use(express.json());

// 简单的测试路由
app.get("/", (req, res) => {
  res.json({ message: "服务器正在运行", timestamp: new Date().toISOString() });
});

app.get("/api/test", (req, res) => {
  res.json({ message: "API测试成功", timestamp: new Date().toISOString() });
});

// 模拟登录路由
app.post("/api/users/login", (req, res) => {
  console.log('收到登录请求:', req.body);
  const { username, password } = req.body;
  
  if (!username || !password) {
    return res.status(400).json({ msg: "请提供用户名和密码" });
  }
  
  // 模拟登录验证
  if (username === "test" && password === "test") {
    res.json({
      token: "fake-token-for-testing",
      user: {
        id: 1,
        username: "test",
        email: "<EMAIL>",
        is_admin: false
      }
    });
  } else {
    res.status(400).json({ msg: "用户名或密码错误" });
  }
});

const PORT = 5000;
const server = app.listen(PORT, () => {
  console.log(`简单服务器运行在端口 ${PORT}`);
  console.log(`测试地址: http://localhost:${PORT}`);
  console.log(`API测试: http://localhost:${PORT}/api/test`);
});

// 保持服务器运行
process.on('SIGINT', () => {
  console.log('\n收到中断信号，关闭服务器...');
  server.close(() => {
    console.log('服务器已关闭');
    process.exit(0);
  });
});

console.log('服务器启动完成，按 Ctrl+C 停止');
