const express = require("express");
const router = express.Router();
const userController = require("../controllers/userController");
const auth = require("../middleware/auth");

// 注册新用户
router.post("/register", userController.register);

// 用户登录
router.post("/login", userController.login);

// 获取当前用户信息 (需要认证)
router.get("/me", auth, userController.getCurrentUser);

// 刷新令牌 (需要认证)
router.post("/refresh-token", auth, userController.refreshToken);

module.exports = router;
